import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    backendToken?: string
    backendUser?: any
    account?: {
      provider?: string
    }
  }

  interface Account {
    backendToken?: string
    backendUser?: any
    authProvider?: string
    redirectToPricing?: boolean
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    backendToken?: string
    backendUser?: any
    provider?: string
    authProvider?: string
    redirectToPricing?: boolean
  }
}
