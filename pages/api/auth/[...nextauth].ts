import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import FacebookProvider from 'next-auth/providers/facebook';
import AppleProvider from 'next-auth/providers/apple';
import { generateAppleClientSecret } from 'lib/auth/generate-apple-secret';
const {
  GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET,
  FACEBOOK_CLIENT_ID,
  FACEBOOK_CLIENT_SECRET,
  APPLE_CLIENT_ID,
  NEXTAUTH_SECRET,
} = process.env;

export default NextAuth({
  debug: true, // Enable debug logging for troubleshooting
  logger: {
    error(code, metadata) {
      console.error('NextAuth Error:', code, metadata);
    },
    warn(code) {
      console.warn('NextAuth Warning:', code);
    },
    debug(code, metadata) {
      console.log('NextAuth Debug:', code, metadata);
    },
  },
  providers: [
    GoogleProvider({
      clientId: GOOGLE_CLIENT_ID!,
      clientSecret: GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),
    AppleProvider({
      clientId: APPLE_CLIENT_ID!,
      clientSecret: generateAppleClientSecret(),
      client: {
        redirect_uris: [`${process.env.NEXTAUTH_URL}/api/auth/callback/apple`], // Enforce full path for token request
      },
      authorization: {
        params: {
          scope: 'name email',
          response_mode: 'form_post',
          state: 'apple_pricing_redirect', // Custom state to identify Apple login for pricing redirect
        },
      },
      checks: [], // Already disabled—good
      httpOptions: {
        timeout: 10000,
      },
    }),

    // FacebookProvider({
    //   clientId: FACEBOOK_CLIENT_ID!,
    //   clientSecret: FACEBOOK_CLIENT_SECRET!,
    //   authorization: {
    //     params: {
    //       auth_type: 'reauthenticate'
    //     }
    //   }
    // }),
  ],
  secret: NEXTAUTH_SECRET!,
  callbacks: {
    signIn: async ({ user, account, profile }) => {
      try {
        console.log('🔐 NextAuth signIn callback triggered');
        console.log('👤 User:', { name: user.name, email: user.email });
        console.log('🏪 Provider:', account?.provider);
        console.log(
          '🎫 Access Token:',
          account?.access_token ? 'Present' : 'Missing'
        );

        // Only process for social providers (Google/Facebook/Apple)
        if (!account?.provider) {
          console.log('❌ Missing provider');
          return false;
        }

        // For Apple, we need id_token instead of access_token
        if (account.provider === 'apple') {
          console.log('🍎 Apple Sign-In Details:');
          console.log('- ID Token:', account?.id_token ? 'Present' : 'Missing');
          console.log(
            '- Access Token:',
            account?.access_token ? 'Present' : 'Missing'
          );
          console.log('- Account Type:', account?.type);
          console.log('- Provider Account ID:', account?.providerAccountId);
          console.log(
            '- Authorization Code:',
            account?.code ? 'Present' : 'Missing' // Fixed: Use account.code
          );

          if (!account?.id_token) {
            console.log('❌ Missing Apple id_token');
            console.log(
              '🔍 Debugging Apple auth failure - checking authorization code...'
            );
            if (account?.authorization_code) {
              console.log(
                '✅ Authorization code is present, but token exchange failed'
              );
            } else {
              console.log(
                '❌ Authorization code is missing - this indicates OAuth flow issue'
              );
            }
            return false;
          }
        }

        // For Google/Facebook, we need access_token
        if (
          (account.provider === 'google' || account.provider === 'facebook') &&
          !account?.access_token
        ) {
          console.log('❌ Missing access token for Google/Facebook');
          return false;
        }

        // Determine the backend endpoint based on provider
        const backendBaseUrl =
          process.env.NEXT_PUBLIC_API_PREFIX_REST ||
          'https://api-dev.fitsomnia.com/api';
        let backendUrl: string;
        if (account.provider === 'google') {
          backendUrl = `${backendBaseUrl}/user-auth/google/sign-in`;
        } else if (account.provider === 'facebook') {
          backendUrl = `${backendBaseUrl}/user-auth/facebook/sign-in`;
        } else if (account.provider === 'apple') {
          backendUrl = `${backendBaseUrl}/user-auth/apple/sign-in`;
        } else {
          console.log('❌ Unsupported provider:', account.provider);
          return false;
        }

        console.log('🔄 Calling backend API:', backendUrl);

        // Prepare request body based on provider
        let requestBody: any;
        if (account.provider === 'apple') {
          requestBody = {
            idToken: account.id_token,
            name: user.name || undefined,
            platform: 'web', // Backend will use service ID for web
          };
        } else {
          requestBody = {
            accessToken: account.access_token,
          };
        }

        // Call your NestJS backend to create/authenticate user
        const response = await fetch(backendUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });

        const data = await response.json();
        console.log('🔄 Backend response status:', response.status);
        console.log('🔄 Backend response data:', data);

        // Check if backend authentication was successful
        if (
          (response.status === 200 || response.status === 201) &&
          data.data?.token
        ) {
          console.log('✅ Backend authentication successful');
          // Store backend JWT in the account object for later use
          account.backendToken = data.data.token;
          account.backendUser = data.data.user || data.user;

          // Store provider for redirect logic - specifically mark Apple for pricing redirect
          account.authProvider = account.provider;
          if (account.provider === 'apple') {
            account.redirectToPricing = true;
          }

          return true;
        } else {
          console.log('❌ Backend authentication failed');
          return false;
        }
      } catch (error) {
        console.error('💥 Error in signIn callback:', error);
        return false;
      }
    },
    redirect: async ({ url, baseUrl }) => {
      console.log('🔄 NextAuth redirect callback:', { url, baseUrl });

      try {
        // Parse the URL to check for callbackUrl parameter
        const urlObj = new URL(url, baseUrl);
        const callbackUrl = urlObj.searchParams.get('callbackUrl');

        console.log('🔍 Parsed URL details:', {
          pathname: urlObj.pathname,
          searchParams: urlObj.searchParams.toString(),
          callbackUrl: callbackUrl
        });

        // Check for Apple login state parameter or callback URL patterns
        const state = urlObj.searchParams.get('state');

        // Check if this is an Apple login callback or if callbackUrl points to pricing
        if (urlObj.pathname.includes('/callback/apple') ||
            state === 'apple_pricing_redirect' ||
            callbackUrl === '/pricing' ||
            url.includes('callbackUrl=%2Fpricing') ||
            url.includes('callbackUrl=/pricing')) {
          console.log('🍎 Apple login detected via URL/state, redirecting to pricing page');
          return `${baseUrl}/pricing`;
        }

        // If there's a valid callbackUrl, use it
        if (callbackUrl && callbackUrl.startsWith('/')) {
          console.log('📍 Using callbackUrl:', callbackUrl);
          return `${baseUrl}${callbackUrl}`;
        }

      } catch (error) {
        console.error('❌ Error parsing redirect URL:', error);
      }

      // Allows relative callback URLs
      if (url.startsWith('/')) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
    session: async ({ session, user, token }) => {
      // Add account provider information to session
      if (token?.provider) {
        (session as any).account = { provider: token.provider };
      }
      // Add backend token and user data to session
      if (token?.backendToken) {
        (session as any).backendToken = token.backendToken;
      }
      if (token?.backendUser) {
        (session as any).backendUser = token.backendUser;
      }
      return session;
    },
    jwt: ({ token, account, user }) => {
      // Store access token and provider
      if (account?.access_token) {
        token.access_token = account.access_token;
      }
      if (account?.provider) {
        token.provider = account.provider;
      }
      // Store backend token and user data
      if (account?.backendToken) {
        token.backendToken = account.backendToken;
      }
      if (account?.backendUser) {
        token.backendUser = account.backendUser;
      }
      // Store auth provider for redirect logic
      if (account?.authProvider) {
        token.authProvider = account.authProvider;
      }
      // Store Apple pricing redirect flag
      if (account?.redirectToPricing) {
        token.redirectToPricing = account.redirectToPricing;
      }
      return token;
    },
  },
});
